package com.yxt.talent.rv.application.xpd.grid;

import com.alibaba.fastjson2.JSON;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.export.I18nComponent;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.talent.rv.application.xpd.common.dto.XpdGridInitBO;
import com.yxt.talent.rv.application.xpd.common.enums.GridCellTemplateEnum;
import com.yxt.talent.rv.application.xpd.common.enums.GridConfigTypeEnum;
import com.yxt.talent.rv.application.xpd.common.enums.GridTypeEnum;
import com.yxt.talent.rv.application.xpd.xpd.XpdInitDataService;
import com.yxt.talent.rv.controller.common.viewobj.PrjLabelVO;
import com.yxt.talent.rv.controller.common.viewobj.XpdGridCellVO;
import com.yxt.talent.rv.controller.manage.prj.dim.viewobj.PrjDimVO;
import com.yxt.talent.rv.controller.manage.xpd.grid.command.XpdGridCombCmd;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.XpdGridDetailVO;
import com.yxt.talent.rv.controller.manage.xpd.rule.viewobj.XpdDimVo;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class XpdGridAppService {
    private final XpdGridMapper gridMapper;
    private final XpdGridCellMapper gridCellMapper;
    private final XpdGridLevelMapper gridLevelMapper;
    private final XpdGridRatioMapper gridRatioMapper;
    private final XpdGridDimCombMapper xpdGridDimCombMapper;
    private final XpdGridMapper xpdGridMapper;
    private final I18nComponent i18nComponent;
    private final XpdLevelMapper xpdLevelMapper;
    private final SpRuleService spRuleService;
    private final XpdInitDataService xpdInitDataService;
    private final XpdGridCellMapper xpdGridCellMapper;
    private final AppProperties appProperties;
    private final XpdDimMapper xpdDimMapper;
    private final SpsdAclService spsdAclService;
    private final XpdDimCombMapper xpdDimCombMapper;

    /**
     * 创建宫格设置，同时生成 九宫格，十六宫格，，，
     *
     * @param xpdGrid
     */
    public void createGridCell(XpdGridPO xpdGrid, List<XpdGridDimCombPO> dimCombPOList, String userId , String xpdId){
        Integer configType = xpdGrid.getConfigType();

        List<XpdGridCellPO> tempCellList = new ArrayList<>();
        if (xpdGrid.getGridType() == 0) {
            tempCellList = initGridCell(xpdGrid.getOrgId(), userId, GridTypeEnum.FOUR_GRID);
        } else if (xpdGrid.getGridType() == 1) {
            tempCellList = initGridCell(xpdGrid.getOrgId(), userId, GridTypeEnum.NINE_GRID);
        } else if (xpdGrid.getGridType() == 2) {
            tempCellList = initGridCell(xpdGrid.getOrgId(), userId, GridTypeEnum.SIXTEEN_GRID);
        }

        List<XpdGridCellPO> newCellList = new ArrayList<>();
        if (configType == 0) {
            for (XpdGridCellPO xpdGridCellPO : tempCellList) {
                xpdGridCellPO.setId(ApiUtil.getUuid());
                xpdGridCellPO.setOrgId(xpdGrid.getOrgId());
                xpdGridCellPO.setXpdId(xpdId);
                xpdGridCellPO.setGridId(xpdGrid.getId());
                xpdGridCellPO.setDimCombId(EMPTY);
                EntityUtil.setAuditFields(xpdGridCellPO, userId) ;
                newCellList.add(xpdGridCellPO);
            }
        } else {
            // 1-按维度组合配置
            for (XpdGridDimCombPO gridDimCombPO : dimCombPOList) {
                for (XpdGridCellPO xpdGridCellPO : tempCellList) {
                    XpdGridCellPO newGridCellPO = new XpdGridCellPO();
                    BeanCopierUtil.copy(xpdGridCellPO, newGridCellPO);
                    newGridCellPO.setId(ApiUtil.getUuid());
                    newGridCellPO.setOrgId(xpdGrid.getOrgId());
                    newGridCellPO.setXpdId(xpdId);
                    newGridCellPO.setGridId(xpdGrid.getId());
                    newGridCellPO.setDimCombId(gridDimCombPO.getDimCombId());
                    EntityUtil.setAuditFields(newGridCellPO, userId) ;
                    newCellList.add(newGridCellPO);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(newCellList)) {
            gridCellMapper.insertBatch(newCellList);
        }

        // 分级标准

    }

    private String getI18Msg(String param){
        return i18nComponent.getI18nValue(param);
    }

    /**
     *
     *
     * @param xpdGrid
     * @param userId
     * @param init 1是创建，0编辑
     */
    public void createGridLevel(XpdGridPO xpdGrid, String userId, Integer init, String xpdId){
        // 0-四宫格,1-九宫格,2-十六宫格
        Integer gridType = xpdGrid.getGridType();
        List<XpdGridLevelPO> list = new ArrayList<>();
        // todo 后期配置国际化，分层名称的国际化code，每次都需要生产一个新的国际化code
        if (gridType == 1) {
            init6Grid(xpdGrid, userId, list, xpdId);
        } else if (gridType == 0){
            init4Grid(xpdGrid, userId, list, xpdId);
        } else {
            init16Grid(xpdGrid, userId, list, xpdId);
        }
        // 创建时 初始化 人才层级
        if (init == 1) {
            gridLevelMapper.insertBatch(list);
            initXpdLevel(xpdGrid, userId, list);
        } else {
            List<XpdGridLevelPO> gridLevelPOS = gridLevelMapper.listByGridId(xpdGrid.getOrgId(), xpdGrid.getId());
            if (gridLevelPOS.size() != list.size()) {
                return;
            }
            List<XpdGridLevelPO> newGridLevelList =
                list.stream().sorted(Comparator.comparing(XpdGridLevelPO::getOrderIndex)).toList();
            for (int i = 0; i < gridLevelPOS.size(); i++) {
                XpdGridLevelPO newGridLevelPO = newGridLevelList.get(i);
                XpdGridLevelPO xpdGridLevelPO = gridLevelPOS.get(i);
                xpdGridLevelPO.setLevelName(newGridLevelPO.getLevelName());
                //
                xpdGridLevelPO.setLevelNameI18n("");
                EntityUtil.setUpdate(xpdGridLevelPO, userId);
            }
            gridLevelMapper.updateBatch(gridLevelPOS);
        }
    }

    public void initXpdLevel(XpdGridPO xpdGrid, String userId, List<XpdGridLevelPO> newGridLevelList) {
        Integer gridType = xpdGrid.getGridType();
        String orgId = xpdGrid.getOrgId();
        String gridId = xpdGrid.getId();

        XpdGridInitBO xpdGridInitBO = xpdInitDataService.gridInitBO(gridType);
        List<XpdLevelPO> levelList = xpdGridInitBO.getLevelList();
        List<XpdGridLevelPO> gridLevelList = xpdGridInitBO.getGridLevelList();
        if (newGridLevelList.size() != gridLevelList.size()) {
            return;
        }
        List<String> newGridLevelIds = newGridLevelList.stream()
            .sorted(Comparator.comparing(XpdGridLevelPO::getOrderIndex))
            .map(XpdGridLevelPO::getId)
            .toList();

        List<String> oldGridLevelIds = gridLevelList.stream()
            .sorted(Comparator.comparing(XpdGridLevelPO::getOrderIndex))
            .map(XpdGridLevelPO::getId)
            .toList();
        int size = newGridLevelList.size();
        Map<String, String> columnIdMap = new HashMap<>();
        for (int i = 0; i < size; i++) {
            String oldGridLevelId = oldGridLevelIds.get(i);
            String newGridLevelId = newGridLevelIds.get(i);
            columnIdMap.put(oldGridLevelId, newGridLevelId);
        }

        /*List<SpRuleGroupBean> conditions = new ArrayList<>();*/
        for (XpdLevelPO xpdLevelPO : levelList) {
            SpRuleBean ruleBeanOne = JSON.parseObject(xpdLevelPO.getFormula(), SpRuleBean.class);
            if (ruleBeanOne != null) {
                spRuleService.replaceIdMap(ruleBeanOne, null,
                    columnIdMap,null);
            }
            xpdLevelPO.setFormula(JSON.toJSONString(ruleBeanOne));
            xpdLevelPO.setId(ApiUtil.getUuid());
            xpdLevelPO.setOrgId(orgId);
            xpdLevelPO.setXpdId(AppConstants.TEMPLATE_XPD_ID);
            xpdLevelPO.setGridId(gridId);
            xpdLevelPO.setLevelNameI18n("");
            EntityUtil.setAuditFields(xpdLevelPO, userId);
        }
        xpdLevelMapper.batchInsert(levelList);


        /*// 获取内置宫格数据
        XpdGridPO xpdGridPO = gridMapper.selectByGridIdAndType(AppConstants.SAAS_ORG_ID, gridType);
        if (xpdGridPO == null) {
            log.error("initXpdLevel error");
            return;
        }
        List<XpdLevelPO> xpdLevelPOS = xpdLevelMapper.listByGridId(AppConstants.SAAS_ORG_ID, xpdGridPO.getId());
        for (XpdLevelPO xpdLevelPO : xpdLevelPOS) {
            xpdLevelPO.setId(ApiUtil.getUuid());
            xpdLevelPO.setOrgId(orgId);
            xpdLevelPO.setGridId(gridId);
            xpdLevelPO.setXpdRuleId(AppConstants.SAAS_ORG_ID);
            EntityUtil.setAuditFields(xpdLevelPO, userId);
        }
        xpdLevelMapper.batchInsert(xpdLevelPOS);*/

    }

    private void init16Grid(XpdGridPO xpdGrid, String userId, List<XpdGridLevelPO> list, String xpdId) {
        String[] param = new String[]{"apis.sptalentrv.xpd.grid.level.poor"
            , "apis.sptalentrv.xpd.grid.level.general"
            ,"apis.sptalentrv.xpd.grid.level.good"
            ,"apis.sptalentrv.xpd.grid.level.excellent"};
        for (int i = 1; i <= 4; i++) {
            XpdGridLevelPO gridLevel = new XpdGridLevelPO();
            gridLevel.setId(ApiUtil.getUuid());
            gridLevel.setOrgId(xpdGrid.getOrgId());
            gridLevel.setXpdId(xpdId);
            gridLevel.setGridId(xpdGrid.getId());
            gridLevel.setLevelName(getI18Msg(param[i-1]));
            gridLevel.setLevelNameI18n(param[i-1]);
            gridLevel.setOrderIndex(i);
            gridLevel.setDeleted(0);
            EntityUtil.setAuditFields(gridLevel, userId);
            list.add(gridLevel);
        }

    }

    private void init4Grid(XpdGridPO xpdGrid, String userId, List<XpdGridLevelPO> list, String xpdId) {
        String[] param = new String[]{"apis.sptalentrv.xpd.grid.level.low"
            ,"apis.sptalentrv.xpd.grid.level.high"};
        for (int i = 1; i <= 2; i++) {
            XpdGridLevelPO gridLevel = new XpdGridLevelPO();
            gridLevel.setId(ApiUtil.getUuid());
            gridLevel.setOrgId(xpdGrid.getOrgId());
            gridLevel.setXpdId(xpdId);
            gridLevel.setGridId(xpdGrid.getId());
            gridLevel.setLevelName(getI18Msg(param[i-1]));
            gridLevel.setLevelNameI18n(param[i-1]);
            gridLevel.setOrderIndex(i);
            gridLevel.setDeleted(0);
            EntityUtil.setAuditFields(gridLevel, userId);
            list.add(gridLevel);
        }
    }


    private void init6Grid(XpdGridPO xpdGrid, String userId, List<XpdGridLevelPO> list, String xpdId) {

        String[] param = new String[]{"apis.sptalentrv.xpd.grid.level.low"
            ,"apis.sptalentrv.xpd.grid.level.middle"
            ,"apis.sptalentrv.xpd.grid.level.high"};
        for (int i = 1; i <= 3; i++) {
            XpdGridLevelPO gridLevel = new XpdGridLevelPO();
            gridLevel.setId(ApiUtil.getUuid());
            gridLevel.setOrgId(xpdGrid.getOrgId());
            gridLevel.setXpdId(xpdId);
            gridLevel.setGridId(xpdGrid.getId());
            gridLevel.setLevelName(getI18Msg(param[i-1]));
            gridLevel.setLevelNameI18n(param[i-1]);
            gridLevel.setOrderIndex(i);
            gridLevel.setDeleted(0);
            EntityUtil.setAuditFields(gridLevel, userId);
            list.add(gridLevel);
        }
    }

    public void resetGrid(XpdGridPO xpdGrid, String userId){
        // 删除旧的模板配置
        gridCellMapper.deleteGrid(xpdGrid.getOrgId(), xpdGrid.getId(), userId);
        // 删除旧的  分级标准
        //gridLevelMapper.deleteGridLevel(xpdGrid.getOrgId(), xpdGrid.getId(), userId);
        // 删除落位比例
        gridRatioMapper.deleteGridRatio(xpdGrid.getOrgId(), xpdGrid.getId(), userId);



    }

    public List<XpdGridCellPO> initGridCell(String orgId, String userId, GridTypeEnum gridType) {
        List<XpdGridCellPO> cells = new ArrayList<>();
        for (GridCellTemplateEnum value : GridCellTemplateEnum.values()) {
            if (value.getGridType().equals(gridType)) {
                XpdGridCellPO gridCell = new XpdGridCellPO();
                gridCell.initData(userId);
                gridCell.setOrgId(orgId);
                gridCell.setCellName(value.getCellIndex() + "号位");
                gridCell.setCellNameI18n(String.format("apis.sptalentrv.grid.cell.name.%s", value.getCellIndex()));
                gridCell.setCellColor(value.getCellColor());
                gridCell.setCellIndex(value.getCellIndex());
                gridCell.setXIndex(value.getX());
                gridCell.setYIndex(value.getY());
                cells.add(gridCell);
            }
        }
        return cells;
    }

    /**
     *
     *
     * @param id gridId
     * @return
     */
    public XpdGridDetailVO findGrid4Edit(String id){
        XpdGridDetailVO res = new XpdGridDetailVO();
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(id);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");
        BeanCopierUtil.copy(xpdGrid, res);
        List<XpdGridCombCmd> xpdGridCombCmds = new ArrayList<>();
        List<XpdGridDimCombPO> dimCombList =
            xpdGridDimCombMapper.findTempByGridId(xpdGrid.getOrgId(), xpdGrid.getId());

        for (XpdGridDimCombPO gridDimComb : dimCombList) {
            XpdGridCombCmd gridCombCmd = new XpdGridCombCmd();
            gridCombCmd.setDimCombId(gridDimComb.getDimCombId());
            gridCombCmd.setShowType(gridDimComb.getShowType());
            xpdGridCombCmds.add(gridCombCmd);
        }
        res.setXpdGridCombCmds(xpdGridCombCmds);
        return res;
    }

    public void chkName(String orgId, String name){
        int countByName = xpdGridMapper.findCountByName(orgId, name);
        if (countByName > 0) {
            throw new ApiException(ExceptionKeys.XPD_GRID_NAME_REPEAT);
        }
    }

    public void chkDimCombShow(List<XpdGridCombCmd> xpdGridCombCmds){
        int sum = xpdGridCombCmds.stream().mapToInt(XpdGridCombCmd::getShowType).sum();
        if (sum > 1) {
            throw new ApiException(ExceptionKeys.XPD_GRID_DEFAULT_SHOW_ONE);
        }
        if (sum == 0) {
            throw new ApiException(ExceptionKeys.XPD_GRID_DEFAULT_SHOW_ZERO);
        }
    }

    public void chkDimCombRepeat(List<XpdGridCombCmd> xpdGridCombCmds){
        Set<String> collect = xpdGridCombCmds.stream().map(XpdGridCombCmd::getDimCombId).collect(Collectors.toSet());
        if (collect.size() != xpdGridCombCmds.size()) {
            throw new ApiException(ExceptionKeys.XPD_GRID_DIM_COMB_REPEAT);
        }
    }

    /**
     * 获取盘点宫格下的维度组信息
     *
     * @param orgId
     * @param gridId
     * @param xpdId
     * @param dimCombId
     * @return
     */
    public List<XpdGridCellVO> getXpdGridCells(String orgId, String gridId, String xpdId, String dimCombId) {
        XpdGridPO xpdGrid = xpdGridMapper.selectByPrimaryKey(gridId);
        Validate.isNotNull(xpdGrid, ExceptionKeys.XPD_GRID_NOT_FOUND);

        if (GridConfigTypeEnum.of(xpdGrid.getConfigType()).isDimensionBased()) {
            // 按维度组合配置时查询格子信息时维度组id不能为空
            Validate.isNotBlank(dimCombId, ExceptionKeys.XPD_DIM_COMB_ID_EMPTY);
        } else {
            // 统一配置时维度组id必须为为空串
            dimCombId = EMPTY;
        }

        List<XpdGridCellPO> xpdGridCells =
            xpdGridCellMapper.selectByXpdIdAndGridIdAndDimCombId(orgId, xpdId, gridId, dimCombId);
        List<XpdGridCellVO> xpdGridCellVos = XpdGridCellVO.Assembler.INSTANCE.toXpdGridCellVos(xpdGridCells);

        // 填充文字颜色
        for (XpdGridCellVO xpdGridCellVO : xpdGridCellVos) {
            xpdGridCellVO.setTextColor(
                appProperties.getGridColorMap().get(xpdGridCellVO.getCellColor().replace("#", "")));
        }
        return xpdGridCellVos;
    }

    public List<PrjDimVO> getXpdDimList(String orgId, String projectId) {
        List<PrjDimVO> result = new ArrayList<>();

        List<XpdDimPO> dimList = xpdDimMapper.listByXpdId(orgId, projectId);
        if (CollectionUtils.isNotEmpty(dimList)) {
            List<String> dimIds = dimList.stream().map(XpdDimPO::getSdDimId).toList();
            // 补充维度名称
            List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(orgId, dimIds);
            Map<String, String> dimMap =
                StreamUtil.list2map(baseDimDetails, DimensionList4Get::getId, DimensionList4Get::getDmName);

            dimList.forEach(item -> {
                PrjDimVO bean = new PrjDimVO();
                BeanCopierUtil.copy(item, bean);
                bean.setDimensionName(dimMap.get(item.getSdDimId()));
                bean.setDimensionType(item.getDimType());
                result.add(bean);
            });
        }
        if (CollectionUtils.isNotEmpty(result)) {
            return result.stream().sorted(Comparator.comparing(PrjDimVO::getDimensionType))
                .collect(Collectors.toList());
        }
        return result;
    }

    public  List<PrjLabelVO> getGridLabels(String orgId, String xpdId, String dimCombId){
        XpdGridPO xpdGridPO = xpdGridMapper.selectByXpdId(orgId, xpdId);
        // 宫格布局
        Integer configType = xpdGridPO.getConfigType();
        if (configType == 0) {
            dimCombId = StringUtils.EMPTY;
        }
        List<XpdGridCellPO> xpdGridCellPOS =
            gridCellMapper.listByGridIdAndDimCombId(orgId, xpdGridPO.getId(), dimCombId);
        List<PrjLabelVO> resList = new ArrayList<>();

        for (XpdGridCellPO xpdGridCell : xpdGridCellPOS) {
            PrjLabelVO res = new PrjLabelVO();
            res.setId(xpdGridCell.getId());
            res.setLabelName(xpdGridCell.getCellName());
            res.setLabelColor(xpdGridCell.getCellColor());
            if (StringUtils.isNotBlank(xpdGridCell.getCellColor())) {
                //
                res.setTextColor(appProperties.getGridColorMap().get(xpdGridCell.getCellColor().replace("#", "")));
            }
            res.setLabelSort(xpdGridCell.getCellIndex());
            resList.add(res);
        }
        return resList;
    }

    /**
     * 刷新第三维颜色
     *
     * @param gridId
     */
    public void refreshThirdDimColor(String gridId) {
        XpdGridPO xpdGrid = gridMapper.selectByPrimaryKey(gridId);
        Validate.isNotNull(xpdGrid, ExceptionKeys.XPD_GRID_NOT_FOUND);

        String orgId = xpdGrid.getOrgId();

        // 维度分层
        List<XpdGridLevelPO> gridLevelList = gridLevelMapper.listByGridId(orgId, gridId);
        for (XpdGridLevelPO xpdGridLevel : gridLevelList) {
            xpdGridLevel.setThirdDimColor(xpdGridLevel.decideThirdDimColor(xpdGrid, appProperties));
            xpdGridLevel.setUpdateTime(LocalDateTime.now());
            xpdGridLevel.setUpdateUserId("refresh");
        }
        gridLevelMapper.updateBatch(gridLevelList);
    }

    /**
     * @param orgId 机构ID
     * @param xpdId 盘点项目ID
     * @return 排序维度列表
     */
    public List<XpdDimVo> getXpdDims(String orgId, String xpdId, int caseType, String dimCombId) {
        // 获取项目下所有维度
        List<XpdDimPO> xpdDimList = xpdDimMapper.listByXpdId(orgId, xpdId);
        if (CollectionUtils.isEmpty(xpdDimList)) {
            return new ArrayList<>();
        }

        // 填充维度名称
        List<String> sdDimIds = BeanCopierUtil.convertList(xpdDimList, XpdDimPO::getSdDimId);
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(orgId, sdDimIds);
        Map<String, DimensionList4Get> dimMap =
            StreamUtil.list2map(baseDimDetails, DimensionList4Get::getId, Function.identity());

        for (XpdDimPO xpdDim : xpdDimList) {
            DimensionList4Get dimensionList4Get = dimMap.get(xpdDim.getSdDimId());
            if (dimensionList4Get == null) {
                continue;
            }
            xpdDim.setSdDimName(dimensionList4Get.getDmName());
            xpdDim.setSdDimNameI18n(dimensionList4Get.getNameI18n());
        }

        // 获取项目所有维度
        if (caseType == 0) {
            // 项目维度
            return xpdDimList.stream().map(XpdDimVo::new).collect(Collectors.toList());
        }

        // 获取除给定维度组维度以外的其他维度
        Validate.isNotBlank(dimCombId, ExceptionKeys.XPD_DIM_COMB_ID_EMPTY);
        XpdDimCombPO dimComb = xpdDimCombMapper.selectByPrimaryKey(dimCombId);
        Validate.isNotNull(dimComb, ExceptionKeys.XPD_DIM_COMB_NOT_FOUND);

        // 获取除给定维度组维度以外的其他维度
        return xpdDimList.stream()
            .filter(xpdDim -> !dimComb.getXSdDimId().equals(xpdDim.getSdDimId()) &&
                              !dimComb.getYSdDimId().equals(xpdDim.getSdDimId()))
            .map(XpdDimVo::new)
            .toList();
    }
}
