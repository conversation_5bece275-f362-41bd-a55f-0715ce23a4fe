package com.yxt.talent.rv.application.dmp.user.legacy;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.spsdk.audit.AuditLogHooker;
import com.yxt.talent.rv.application.dmp.DmpCmdAppService;
import com.yxt.talent.rv.application.dmp.calc.DmpCalculator;
import com.yxt.talent.rv.application.dmp.task.legacy.DmpTaskAppService;
import com.yxt.talent.rv.controller.manage.dmp.command.DmpUserAddCmd;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpUseAddVO;
import com.yxt.talent.rv.domain.dmp.msg.DynamicGroupUserChangeData;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.NumberEnum;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.conf.DmpConfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserAutoGroupMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpUserAutoGroupPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.conf.DmpConfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserPO;
import com.yxt.talent.rv.infrastructure.service.file.command.FileImportCmd;
import com.yxt.talent.rv.infrastructure.service.file.dto.FileImportResult;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.udpfacade.bean.user.es.search.EsGroupSearchCriteria;
import com.yxt.udpfacade.bean.user.es.search.EsUserInfoVo;
import com.yxt.udpfacade.bean.user.es.search.EsUserSearchParam;
import com.yxt.udpfacade.enums.SourceFromEnum;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.domain.dmp.Dmp.Status.IN_PROGRESS;
import static com.yxt.talent.rv.domain.dmp.Dmp.Status.PAUSED;

@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class DmpUserAppManage {

    private final DmpUserAppService dmpUserAppService;
    private final DmpCalculator dmpCalculator;
    private final DmpTaskAppService dmpTaskAppService;
    private final SpevalAclService spevalAclService;
    private final DmpMapper dmpMapper;

    private final DmpUserAutoGroupMapper dmpUserAutoGroupMapper;

    private final UdpAclService udpAclService;

    private final DmpCmdAppService dmpCmdAppService;

    private final DmpConfMapper dmpConfMapper;

    private final DmpUserMapper dmpUserMapper;

    /**
     * 动态陪陪项目添加人员
     *
     * @param dmpId
     * @param param
     * @param orgId
     * @param userId
     */
    public DmpUseAddVO addDmpUser(String dmpId, DmpUserAddCmd param, String orgId, String userId) {
        List<DmpUserPO> addDmpUsersRes = new ArrayList<>();
        DmpPO dmp = loadAndValid(orgId, dmpId);
        DmpUseAddVO resVO =
                dmpUserAppService.addDmpUser(dmpId, param, orgId, userId, addDmpUsersRes);
        List<String> addUserIds = addDmpUsersRes.stream()
                .map(DmpUserPO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        // 业务日志只输出最终添加成功的人，排除掉重复添加的人
        log.debug("LOG67440:{}", addUserIds);
        AuditLogHooker.modLogParam(DmpUserAddCmd.class, (param1) -> param1.setUserIds(addUserIds));
        if (CollectionUtils.isEmpty(addUserIds)) {
            return resVO;
        }
        // 判断项目是否有表单类型任务
        long dmpFormCount = dmpTaskAppService.countFormTask(orgId, dmpId);
        if (dmpFormCount > 0L) {
            spevalAclService.batchAddEvalUsers(dmpId, addUserIds, orgId, userId);
        }
        // 判断人员不为空并且项目是进行中、暂停的状态，触发新增人员计算
        addUsersCalculate(orgId, dmpId, dmp.getDmpStatus(), addUserIds, userId);
        return resVO;
    }

    /**
     * 人岗匹配项目导入人员
     *
     * @param orgId
     * @param userId
     * @param dmpId
     * @param bean
     * @param file
     */
    public FileImportResult batchImportExcel(
            String orgId, String userId, String dmpId, FileImportCmd bean, MultipartFile file) {
        List<DmpUserPO> addDmpUsersRes = new ArrayList<>();
        DmpPO dmp = loadAndValid(orgId, dmpId);
        FileImportResult importResult =
                dmpUserAppService.batchImportExcel(orgId, userId, dmpId, bean, file,
                        addDmpUsersRes);
        List<String> addUserIds = addDmpUsersRes.stream()
                .map(DmpUserPO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(addUserIds)) {
            return importResult;
        }
        // 判断项目是否有表单类型任务
        long dmpFormCount = dmpTaskAppService.countFormTask(orgId, dmpId);
        if (dmpFormCount > 0L) {
            spevalAclService.batchAddEvalUsers(dmpId, addUserIds, orgId, userId);
        }
        // 判断人员不为空并且项目是进行中、暂停的状态，触发新增人员计算
        addUsersCalculate(orgId, dmpId, dmp.getDmpStatus(), addUserIds, userId);
        return importResult;
    }

    private void addUsersCalculate(
            String orgId, String dmpId, Integer dmpStatus, List<String> addUserIds,
            String operator) {
        List<Integer> statusList = Arrays.asList(IN_PROGRESS.getCode(), PAUSED.getCode());
        if (!CollectionUtils.isEmpty(addUserIds) && statusList.contains(dmpStatus)) {
            // 将人员切割1000一次调用
            List<List<String>> partUserIdList = ListUtil.partition(addUserIds, 1000);
            for (List<String> partUserId : partUserIdList) {
                // TODO 这里还是改成队列比较好
                dmpCalculator.triggerCalculateDmpUser(orgId, dmpId, partUserId, operator);
            }
        }
    }

    private DmpPO loadAndValid(String orgId, String dmpId) {
        DmpPO dmp = dmpMapper.selectByOrgIdAndId(orgId, dmpId);
        if (dmp == null || dmp.getDeleted() == YesOrNo.YES.getValue()) {
            throw new ApiException(ExceptionKeys.DMP_NOT_EXISTED);
        }
        return dmp;
    }

    /**
     * 自动加人初始化
     *
     * @param orgId
     * @param dmpId
     * @param operator
     */
    public void initAutoAdduser(String orgId, String dmpId, String operator){
        DmpUserAutoGroupPO group = dmpUserAutoGroupMapper.findGroupNoSyncByDmpId(orgId, dmpId);
        DmpPO dmpPO = dmpMapper.selectByOrgIdAndId(orgId, dmpId);
        if (dmpPO == null) {
            throw new ApiException(ExceptionKeys.DMP_NOT_EXISTED);
        }
        if (group == null) {
            return;
        }
        List<String> userIds = new ArrayList<>();
        // 根据 groupId 获取人员
        EsUserSearchParam searchParam = new EsUserSearchParam();
        searchParam.setOrgId(orgId);

        getGroupParam(orgId, group.getGroupId(), searchParam);
        searchParam.setLimit(500);
        searchParam.setProductCode("xxv2");
        searchParam.setSourceFrom(SourceFromEnum.TALENT.getValue());
        searchParam.setSearchFullOrg(1);
        searchParam.setIsOpenLimit(true);
        int offset = 0;
        int pageNum = 1;
        do {
            searchParam.setOffset(offset);
            log.info("LOG13195:initAutoAdduser inmsg={}", JSON.toJSONString(searchParam));
            PagingList<EsUserInfoVo> esUserInfoVoPagingList = udpAclService.search4Page(searchParam);
            List<EsUserInfoVo> esUserInfoVos = esUserInfoVoPagingList.getDatas();
            List<String> tempUserIds = esUserInfoVos.stream().map(EsUserInfoVo::getId).collect(Collectors.toList());
            Paging paging = esUserInfoVoPagingList.getPaging();
            log.info("LOG13205:initAutoAdduser outmsg page={},user={}", JSON.toJSONString(paging), JSON.toJSONString(tempUserIds));
            long afterOffset = paging.getOffset() + paging.getLimit();
            if (pageNum < paging.getPages()) {
                pageNum = pageNum + 1;
                offset = (int) afterOffset;
            } else {
                offset = 0;
            }
            //保存到缓存
            if (!CollectionUtils.isEmpty(tempUserIds)) {
                userIds.addAll(tempUserIds);
            }
        } while (offset > 0);

        List<List<String>> lists = CommonUtil.splitList(userIds, 200);
        for (List<String> list : lists) {
            addUserAuto(orgId, dmpId, operator, list, dmpPO);
        }
        // 将自动加人标识改成已经完成人员初始化
        dmpUserAppService.updateSyncFlag(orgId, dmpId, operator);
    }

    private void addUserAuto(
            String orgId, String dmpId, String operator, List<String> userIds, @NonNull DmpPO dmp) {
        log.info("LOG13565:addUserAuto dmpId={}, userIds={}", dmpId, BeanHelper.bean2Json(userIds));
        List<DmpUserPO> addDmpUsersRes = new ArrayList<>();
        dmpUserAppService.addDmpUser4group(orgId, dmpId, userIds, addDmpUsersRes, operator);

        List<String> addUserIds = addDmpUsersRes.stream()
                .map(DmpUserPO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(addUserIds)) {
            return;
        }
        // 判断项目是否有表单类型任务
        long dmpFormCount = dmpTaskAppService.countFormTask(orgId, dmpId);
        if (dmpFormCount > 0L) {
            spevalAclService.batchAddEvalUsers(dmpId, addUserIds, orgId, operator);
        }
        // 判断人员不为空并且项目是进行中、暂停的状态，触发新增人员计算
        addUsersCalculate(orgId, dmpId, dmp.getDmpStatus(), addUserIds, operator);
    }

    private void getGroupParam(String orgId, String groupId, EsUserSearchParam searchParam){
        EsGroupSearchCriteria groupSearchCriteria = new EsGroupSearchCriteria();
        groupSearchCriteria.setGroupIds(Lists.newArrayList(groupId));
        groupSearchCriteria.setOrgId(orgId);
        EsGroupSearchCriteria searchCriteria = udpAclService.searchEsUserGroupCriteria(groupSearchCriteria);
        EsUserSearchParam resEsSearchParam = udpAclService.getEsSearchParam4UserGroup(searchCriteria);
        resEsSearchParam.setGroupSearch(true);
        BeanCopierUtil.copy(resEsSearchParam, searchParam);
    }

    /**
     * 用户组，加人，减人
     *
     * @param orgId
     * @param dmpId
     * @param bean
     */
    public void groupUserChange(String orgId, String dmpId, DynamicGroupUserChangeData bean){
        log.info("LOG13575:groupUserChange dmpId={}, userIds={}", dmpId, JSON.toJSONString(bean));
        DmpPO dmpPO = dmpMapper.selectByOrgIdAndId(orgId, dmpId);
        if (dmpPO == null) {
            log.error("LOG14265:groupUserChange dmpPO empty dmpId={}", dmpId);
            return;
        }
        // 自动加人
        Set<String> inUserIds = bean.getInUserIds();
        if (!CollectionUtils.isEmpty(bean.getInUserIds())) {
            addUserAuto(orgId, dmpId, "udp-mq", Lists.newArrayList(inUserIds), dmpPO);
        }

        // 自动减人
        DmpConfPO dmpConfPO = dmpConfMapper.selectByDmpId(orgId, dmpId);
        if (dmpConfPO == null) {
            throw new ApiException(ExceptionKeys.DMP_CONF_NOT_EXISTS);
        }
        Set<String> outUserIds = bean.getOutUserIds();
        if (Objects.equals(dmpConfPO.getGroupAutoExit(), NumberEnum.ZERO.getNumber()) || CollectionUtils.isEmpty(outUserIds)) {
            return;
        }
        // 自动加入的人员才能被自动去除
        List<DmpUserPO> dmpUsers = dmpUserMapper.selectAutoUserByDmpIdAndUserIds(orgId, dmpId,
                Lists.newArrayList(outUserIds));
        if (CollectionUtils.isEmpty(dmpUsers)) {
            return;
        }
        List<String> removeUserIds =
                dmpUsers.stream().map(DmpUserPO::getUserId).collect(Collectors.toList());

        dmpUserAppService.delDmpUserAuto(orgId, dmpId, removeUserIds, "udp-mq");

    }

}
