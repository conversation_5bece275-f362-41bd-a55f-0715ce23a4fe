package com.yxt.talent.rv.application.activity.expt;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import com.yxt.talent.rv.infrastructure.service.file.ExcelUtils;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024/12/20
 */
@Component
@RequiredArgsConstructor
public class ImportIndErrorStrategy extends AbstractExportStrategy {
    private final I18nComponent i18nComponent;

    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        DynamicExcelExportContent prjResult = (DynamicExcelExportContent) data;
        ExcelUtils.exportWithDynamicHeader(
            prjResult.getHeaders(), prjResult.getSheets(), prjResult.getData(), filePath);
        return fileName;
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        String taskName = i18nComponent.getI18nValue("apis.sptalentrv.xpd.import.dim.indicator.errFile");
        return buildDownInfo(userCache, fileName, taskName);
    }
}
