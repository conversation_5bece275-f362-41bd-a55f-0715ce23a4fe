package com.yxt.talent.rv.application.todo;

import cn.hutool.core.collection.ListUtil;
import com.yxt.msgfacade.bean.GroupUserInfo;
import com.yxt.msgfacade.bean.Todo4Create;
import com.yxt.msgfacade.bean.Todo4ModifyBatch;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 校准会待办 策略器
 */
@Component
public class CaliMeetSceneStrategy
        extends TodoSceneStrategy<CaliMeetSceneStrategy.CaliMeetTodoInfoDto, CaliMeetSceneStrategy.CaliMeetTodoInfoDto> {

    private final String PARAM_CALI_MEET_NAME = "{{calibrationName}}";
    private final String PARAM_START_TIME = "{{startTime}}";
    private final String PARAM_END_TIME = "{{endTime}}";
    private final String PARAM_URL = "{{url}}";

    public CaliMeetSceneStrategy() {
    }

    @Override
    public TodoSceneEnum getTodoSceneEnum() {
        return TodoSceneEnum.CALI_MEET_START;
    }

    @Override
    protected List<Todo4Create> convert2TodoCreates(String orgId, String opUserId, CaliMeetTodoInfoDto bizParam) {
        return bizParam.caliMeetUsers.stream()
            .map(user -> {

                Map<String, String> params = getParams(bizParam.getMeetName(), bizParam.getStartTime(),
                        bizParam.getEndTime(), bizParam.getUrl());

                GroupUserInfo groupUserInfo = new GroupUserInfo();
                groupUserInfo.setOrgId(orgId);
                groupUserInfo.setUserIds(ListUtil.toList(user.getUserId()));

                Todo4Create create = new Todo4Create();
                create.setSceneCode(getSceneCode());
                create.setOrgId(orgId);
                create.setOrganizer(bizParam.getMeetName());
                create.setOperateUserId(opUserId);
                create.setTodoId(user.getCalimeetId());
                create.setParams(params);
                create.setGroupUserInfos(ListUtil.toList(groupUserInfo));
                create.setJumpUrl(bizParam.getUrl());
                create.setCustomParams(null);

                return create;
            })
            .collect(Collectors.toList());
    }

    @Override
    protected Todo4ModifyBatch convert2Todo4ModifyBatch(String orgId, String opUserId, CaliMeetTodoInfoDto bizParam) {
        Todo4ModifyBatch rvt = new Todo4ModifyBatch();

        rvt.setOrgId(orgId);
        rvt.setSceneCode(getSceneCode());
        rvt.setOperateUserId(opUserId);
        rvt.setTodos(
            convert2Todos4ModifyBatch(bizParam)
        );

        return rvt;
    }

    protected List<Todo4ModifyBatch.Todo> convert2Todos4ModifyBatch(CaliMeetTodoInfoDto bizParam) {
        return bizParam.caliMeetUsers.stream()
            .map(user -> {

                Map<String, String> params = getParams(bizParam.getMeetName(), bizParam.getStartTime(),
                        bizParam.getEndTime(), bizParam.getUrl());

                Todo4ModifyBatch.Todo todo = new Todo4ModifyBatch.Todo();

                todo.setTodoId(user.getCalimeetId());
                todo.setParams(params);
                todo.setJumpUrl(bizParam.getUrl());
                todo.setCustomParams(null);

                return todo;
            })
            .collect(Collectors.toList());
    }


    private Map<String, String> getParams(String meetName, LocalDateTime startTime, LocalDateTime endTime, String jumpUrl) {
        Map<String, String> params = new HashMap<>();
        params.put(PARAM_CALI_MEET_NAME, meetName);
        params.put(PARAM_START_TIME, startTime.toString());
        params.put(PARAM_END_TIME, endTime.toString());
        params.put(PARAM_URL, jumpUrl);
        return params;
    }


    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class CaliMeetTodoInfoDto {

        private String meetName;

        private LocalDateTime startTime;

        private LocalDateTime endTime;

        private String url;

        /**
         * 校准会参与人
         */
        private List<CalimeetParticipantsPO> caliMeetUsers;

    }
}
