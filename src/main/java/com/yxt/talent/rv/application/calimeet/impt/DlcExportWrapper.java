package com.yxt.talent.rv.application.calimeet.impt;

import cn.afterturn.easypoi.exception.excel.ExcelExportException;
import cn.afterturn.easypoi.exception.excel.enums.ExcelExportEnum;
import com.yxt.ubiz.export.bean.ExportFileInfo;
import com.yxt.ubiz.export.common.enums.ExportFileTypeEnum;
import com.yxt.ubiz.export.common.utils.ExportUtil;
import com.yxt.ubiz.export.common.utils.ZipUtil;
import com.yxt.ubiz.export.component.DownFacadeUtil;
import com.yxt.ubiz.export.component.ExportConfigUtil;
import com.yxt.ubiz.export.component.ExportMDUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.function.Supplier;

@Component
@RequiredArgsConstructor
public class DlcExportWrapper {
    private static final Logger log = LoggerFactory.getLogger(DlcExportWrapper.class);

    public Long export(ExportFileInfo exportFileInfo, Supplier<String> supplier) {
        this.checkParams(exportFileInfo);
        if (exportFileInfo.getFileType() == ExportFileTypeEnum.MF) {
            throw new ExcelExportException("单任务导出文件类型不支持Multiple File", ExcelExportEnum.PARAMETER_ERROR);
        } else {
            Long downloadId = this.getDownloadId(exportFileInfo);
            try {
                log.info("导出文件[{}]开始", exportFileInfo.getFileName());
                this.beforeWriteFile(exportFileInfo);
                supplier.get();
                log.info("导出文件[{}]完成", exportFileInfo.getFileName());
                this.afterWriteFile(exportFileInfo);
            } catch (Exception var4) {
                log.error("导出文件[{}]失败 ", exportFileInfo.getFileName(), var4);
            }
            return downloadId;
        }
    }

    protected Long getDownloadId(ExportFileInfo exportFileInfo) {
        if (exportFileInfo.isUsedlc()) {
            Long downloadId = DownFacadeUtil.createInfo(exportFileInfo);
            exportFileInfo.setDownloadId(downloadId);
        }

        return exportFileInfo.getDownloadId() == null ? 0L : exportFileInfo.getDownloadId();
    }

    protected void checkParams(ExportFileInfo exportFileInfo) {
        if (exportFileInfo.getLocale() == null) {
            throw new ExcelExportException("export Locale is required", ExcelExportEnum.PARAMETER_ERROR);
        } else if (StringUtils.isBlank(exportFileInfo.getFileName())) {
            throw new ExcelExportException("export fileName is required", ExcelExportEnum.PARAMETER_ERROR);
        } else if (StringUtils.isBlank(exportFileInfo.getOrgId())) {
            throw new ExcelExportException("export orgId is required", ExcelExportEnum.PARAMETER_ERROR);
        } else if (StringUtils.isBlank(exportFileInfo.getUserId())) {
            throw new ExcelExportException("export userId is required", ExcelExportEnum.PARAMETER_ERROR);
        } else if (StringUtils.isBlank(exportFileInfo.getFullname())) {
            throw new ExcelExportException("export fullname is required", ExcelExportEnum.PARAMETER_ERROR);
        } else if (exportFileInfo.getFileType() == null) {
            throw new ExcelExportException("export fileType is required", ExcelExportEnum.PARAMETER_ERROR);
        }
    }

    protected void beforeWriteFile(ExportFileInfo exportFileInfo) {
        if (exportFileInfo.isUsedlc()) {
            DownFacadeUtil.fileStart(exportFileInfo.getDownloadId());
        }

    }

    protected void afterWriteFile(ExportFileInfo exportFileInfo) {
        String localPath = this.zipHandle(exportFileInfo);
        log.info("afterWriteFile localPath:{}", localPath);
        if (exportFileInfo.isUsedlc()) {
            DownFacadeUtil.sendFinishMq(exportFileInfo.getDownloadId(), localPath, exportFileInfo.getUserId());
        }

    }

    protected String getTempFileDir(ExportFileInfo exportFileInfo) {
        return getExportDlcDir(exportFileInfo);
    }

    private String zipHandle(ExportFileInfo exportFileInfo) {
        String fileExportPath = this.getTempFilePath(exportFileInfo);
        if (exportFileInfo.isNeedZip()) {
            String var10000 = this.getDownPath();
            String zipFileDir = var10000 + exportFileInfo.getOrgId() + File.separator;
            String zipFilePath = zipFileDir + exportFileInfo.getFileName() + ".zip";
            ZipUtil.compressFileToZip(fileExportPath, zipFilePath);

            try {
                Files.delete(Paths.get(fileExportPath));
            } catch (IOException var6) {
                log.error("删除临时文件[{}]失败", fileExportPath);
            }

            return this.getExportDlcZipLocalPath(exportFileInfo);
        } else {
            return this.getExportDlcLocalPath(exportFileInfo);
        }
    }

    public String getLocalFilePath(ExportFileInfo exportFileInfo) {
        String var10000 = this.getDownPath();
        String dir = var10000 + exportFileInfo.getOrgId() + File.separator;
        return exportFileInfo.isNeedZip() ? dir + exportFileInfo.getFileName() + ".zip" : dir + exportFileInfo.getFileName() + exportFileInfo.getFileType().getSuffix();
    }

    protected String getExportDlcDir(ExportFileInfo exportFileInfo) {
        String var10000 = this.getDownPath();
        String dir = var10000 + exportFileInfo.getOrgId() + File.separator;
        ExportUtil.checkAndCreateDir(dir);
        return dir;
    }

    protected String getDownPath() {
        return ExportConfigUtil.getExportConfig().getDownPath();
    }

    protected String getExportDlcZipLocalPath(ExportFileInfo exportFileInfo) {
        String var10000 = File.separator;
        return var10000 + "uperr" + File.separator + exportFileInfo.getOrgId() + File.separator + exportFileInfo.takeFinalFileName() + ".zip";
    }

    protected String getExportDlcLocalPath(ExportFileInfo exportFileInfo) {
        String var10000 = File.separator;
        return var10000 + "uperr" + File.separator + exportFileInfo.getOrgId() + File.separator + exportFileInfo.takeFinalFileName() + exportFileInfo.getFileType().getSuffix();
    }

    protected String getTempFilePath(ExportFileInfo exportFileInfo) {
        String exportFileDir = this.getTempFileDir(exportFileInfo);
        File directory = new File(exportFileDir);
        if (!directory.exists()) {
            boolean mkdirs = directory.mkdirs();
            log.info("exportFileDir:{},result {}", exportFileDir, mkdirs);
        }

        return exportFileDir + exportFileInfo.getFileName() + exportFileInfo.getFileType().getSuffix();
    }
}