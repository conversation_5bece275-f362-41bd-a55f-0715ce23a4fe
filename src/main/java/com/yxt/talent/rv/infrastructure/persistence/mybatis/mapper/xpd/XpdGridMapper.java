package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.controller.manage.xpd.grid.query.XpdGridQuery;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface XpdGridMapper extends CommonMapper<XpdGridPO> {
    int insert(XpdGridPO record);

    int insertOrUpdate(XpdGridPO record);

    XpdGridPO selectByPrimaryKey(String id);

    @Select("select grid_type from rv_xpd_grid where id = #{id}")
    Integer getGridTypeById(String id);

    List<XpdGridPO> listInner(@Param("orgId") String orgId,
        @Param("xpdId") String xpdId);

    IPage<XpdGridPO> selectPage(@Param("page") IPage<XpdGridPO> page, @Param("orgId") String orgId,
                                @Param("query") XpdGridQuery query);

    List<XpdGridPO> selectList( @Param("orgId") String orgId,
        @Param("query") XpdGridQuery query);

    XpdGridPO getTemplateByGridId(@Param("orgId") String orgId,
                                  @Param("gridId") String gridId);

    /**
     * 假删
     */
    void deleteById(@Param("id") String id, @Param("userId") String userId);

    XpdGridPO selectByXpdIdAndSourceGridId(@Param("orgId") String orgId,
                                      @Param("xpdId") String xpdId,
                                      @Param("sourceGridId") String sourceGridId);

    XpdGridPO selectByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    void deleteByXpdId(
            @Param("orgId") String orgId,
            @Param("userId") String userId,
            @Param("xpdId") String xpdId);
    int findCountByName(@Param("orgId") String orgId, @Param("name") String name);

    XpdGridPO selectByGridIdAndType(@Param("orgId") String orgId, @Param("gridType") Integer gridType);

    List<XpdGridPO> selectByXpdIdAndSourceType(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("sourceType") int sourceType,
        @Param("gridType") int gridType);

    List<XpdGridPO> selectByIds(@Param("ids")List<String> gridIds);

    List<String> select4RefreshThirdDimColor();
}