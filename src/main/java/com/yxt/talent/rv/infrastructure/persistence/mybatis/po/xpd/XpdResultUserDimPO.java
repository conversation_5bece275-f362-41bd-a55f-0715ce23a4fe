package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.talent.rv.domain.RvBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 盘点用户维度结果
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_result_user_dim")
public class XpdResultUserDimPO extends RvBaseEntity implements Serializable {
    /**
     * 主键id
     */
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 盘点项目id
     */
    private String xpdId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 冗余的人才标准的维度id
     */
    private String sdDimId;

    /**
     * 宫格分层id, rv_xpd_grid_level.id
     */
    private String gridLevelId;

    /**
     * 分值, 包括绩效得分
     */
    private BigDecimal scoreValue;

    /**
     * 达标率
     */
    private BigDecimal qualifiedPtg;

    /**
     * 冗余的绩效活动评估结果(优秀/良好)id, 绩效维度且按绩效结果计算时有效
     */
    private String perfResultId;

    /**
     * 执行计算批次号
     */
    private Integer calcBatchNo;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否被校准过，0-否 1-是
     */
    private Integer caliFlag;

    /**
     * 被校准结果覆盖之前的原始的计算出来的数据快照
     */
    private String originalSnap;

    @Serial
    private static final long serialVersionUID = 1L;

}