package com.yxt.talent.rv.controller.manage.xpd.log;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.spsdfacade.bean.spsd.ModelInfo;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.application.xpd.xpd.XpdSceneService;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.xpd.log.viewobj.XpdCreateLogDTO;
import com.yxt.talent.rv.infrastructure.common.constant.enums.UTreeEnum;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.RvActivityMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdScene;
import com.yxt.ubiz.tree.domain.entity.UTreeNode;
import com.yxt.ubiz.tree.service.UTreeNodeService;
import jodd.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

@Slf4j
@AllArgsConstructor
@Component
public class XpdUpdateLogProvider implements AuditLogDataProvider<String, XpdCreateLogDTO> {

    private final SptalentsdFacade sptalentsdFacade;
    private final UTreeNodeService uTreeNodeService;
    private final XpdSceneService xpdSceneService;
    private final XpdService xpdService;
    private final ActivityService activityService;

    private static final String DEFAULT_ID = "00000000-0000-0000-0000-000000000000";

    @Override
    public XpdCreateLogDTO before(String id, AuditLogBasicBean logBasic) {
        return getAuditLogData(id, logBasic.getOrgId());
    }

    @Override
    public XpdCreateLogDTO after(String id, AuditLogBasicBean logBasic) {
        return getAuditLogData(id, logBasic.getOrgId());
    }

    private XpdCreateLogDTO getAuditLogData(String id, String orgId) {
        Activity activity = activityService.findById(orgId, id);

        XpdCreateLogDTO res = new XpdCreateLogDTO();
        res.setActvName(activity.getActvName());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = sdf.format(activity.getStartTime());
        String endTime = sdf.format(activity.getEndTime());
        res.setTime(startTime + "~" + endTime);
        res.setDescription(activity.getDescription());

        if (StringUtils.isNotBlank(activity.getModelId())) {
            ModelInfo modelInfo = sptalentsdFacade.getModelInfo(activity.getOrgId(), activity.getModelId());
            if (null != modelInfo) {
                res.setModelName(modelInfo.getTitle());
            }
        }

        if (StringUtils.isNotBlank(activity.getCategoryId())) {
            List<UTreeNode> simpleNodes = uTreeNodeService.getSimpleNodeIds(activity.getOrgId(), UTreeEnum.XPD_BASE.getTreeId(),
                Collections.singletonList(activity.getCategoryId()));
            if (CollectionUtils.isNotEmpty(simpleNodes)) {
                res.setCategoryName(simpleNodes.get(0).getNodeName());
            }
        }

        if (StringUtils.isNotBlank(activity.getSceneId())) {
            XpdScene xpdScene = xpdSceneService.findById(DEFAULT_ID, activity.getSceneId());
            res.setSceneName(xpdScene.getSceneName());
        }

        res.setAutoEndDesc(null == activity.getAutoEnd() || activity.getAutoEnd() == 0 ?  "关闭" : "开启");
        res.setAuditEnabledDesc(null == activity.getAuditEnabled() || activity.getAuditEnabled() == 0 ?  "关闭" : "开启");
        log.info("XpdUpdateLogProvider res={}", JSON.toJSONString(res));
        return res;
    }

    @Override
    public Pair<String, String> entityInfo(
            String id, XpdCreateLogDTO beforeObj, XpdCreateLogDTO afterObj,
            AuditLogBasicBean logBasic) {
        String name = StringPool.EMPTY;
        if (beforeObj != null) {
            name = beforeObj.getActvName();
        } else if (afterObj != null) {
            name = afterObj.getActvName();
        }
        return Pair.of(id, "盘点-" + name);

    }
}
