package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
@Data
public class CaliMeetRecordVO {

    @Schema(description = "数据主键ID")
    private String id;

    @Schema(description = "用户ID")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String userId;

    @Schema(description = "用户账号")
    private String username;

    @Schema(description = "用户名")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String fullname;

    @Schema(description = "用户状态：用于标识当前用户的状态(0-禁用,1-启用, 2-已删除)")
    private Integer status;

    @Schema(description = "用户删除状态：0-未删除,1-已删除")
    private Integer deleted;

    @Schema(description = "部门名称")
    @L10NProperty(resourceType = ResourceTypeEnum.DEPT, shape = ShapeEnum.VALUE)
    private String deptName;

    @Schema(description = "岗位")
    @L10NProperty(resourceType = ResourceTypeEnum.POSITION, shape = ShapeEnum.VALUE)
    private String positionName;

    private String gradeName;

    private String dimCombId;

    @Schema(description = "维度组合名称")
    private String dimCombName;

    @Schema(description = "校准幅度")
    private Integer caliShift;

    @Schema(description = "校准前宫格")
    private String originalCellIndexName;

    private Integer originalCellIndex;

    @Schema(description = "校准后宫格")
    private String cellIndexName;

    private Integer cellIndex;

    private String caliUserId;

    @Schema(description = "校准人")
    private String caliUserName;

    @Schema(description = "校准时间")
    private Date caliTime;

    @Schema(description = "校准会id")
    private String caliMeetId;

    @Schema(description = "校准会名称")
    private String caliMeetName;



}
