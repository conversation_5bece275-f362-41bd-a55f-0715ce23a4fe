package com.yxt.talent.rv.controller.manage.calimeet.query;

import com.yxt.talent.rv.controller.common.query.SearchUdpScopeAuthQuery;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025/5/21
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliResult4Query extends SearchUdpScopeAuthQuery {

    @Schema(description = "盘点项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String projectId;

    @Schema(description = "校准会id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String meetingId;

    @Schema(description = "维度组合id")
    private String dimCombId;

    @Schema(description = "x轴维度id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String axisX;

    @Schema(description = "y轴维度id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String axisY;

    @Schema(description = "格子编号")
    private Integer cellIndex;

    /*@Schema(description = "x轴维度值（1-3对应低至高）")
    private Integer valueX;

    @Schema(description = "y轴维度值（1-3对应低至高）")
    private Integer valueY;

    private List<String> userIds;

    private List<String> authUserIds;*/

}
