package com.yxt.talent.rv.controller.manage.xpd.result.viewobj;

import com.yxt.talent.rv.application.xpd.common.dto.XpdDimCombGridResult;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslate;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "盘点人员维度结果")
public class XpdUserDimResultVO extends UserBaseInfo {

    @Schema(description = "维度id")
    private String sdDimId;

    @Schema(description = "分层id")
    private String gridLevelId;

    @I18nTranslate(codeField = "levelNameI18n")
    @Schema(description = "分层名称")
    private String gridLevelName;

    @Schema(description = "分层名称国际化")
    private String levelNameI18n;

    @Schema(description = "分层序号")
    private Integer orderIndex;

    @Schema(description = "分层值:比例或固定值。结果类型为<得分>时表示分数,结果类型为<达标率>时表示达标率")
    private BigDecimal scoreValue;

    @Schema(description = "达标率")
    private BigDecimal qualifiedPtg;

    public XpdUserDimResultVO(XpdDimCombGridResult e) {
    }
}
