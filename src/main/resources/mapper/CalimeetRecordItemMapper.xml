<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetRecordItemMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetRecordItemPO">
    <!--@mbg.generated-->
    <!--@Table rv_calimeet_record_item-->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="org_id" jdbcType="CHAR" property="orgId" />
    <result column="calimeet_id" jdbcType="CHAR" property="calimeetId" />
    <result column="user_id" jdbcType="CHAR" property="userId" />
    <result column="calimeet_record_id" jdbcType="CHAR" property="calimeetRecordId" />
    <result column="dim_comb_id" jdbcType="CHAR" property="dimCombId" />
    <result column="original_cell_index" jdbcType="INTEGER" property="originalCellIndex" />
    <result column="cell_index" jdbcType="INTEGER" property="cellIndex" />
    <result column="cali_shift" jdbcType="INTEGER" property="caliShift" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_id" jdbcType="CHAR" property="updateUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, calimeet_id, user_id, calimeet_record_id, dim_comb_id, original_cell_index, 
    cell_index, cali_shift, deleted, create_time, create_user_id, update_time, update_user_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_calimeet_record_item
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_calimeet_record_item
    where id = #{id,jdbcType=CHAR}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetRecordItemPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_record_item (id, org_id, calimeet_id, 
      user_id, calimeet_record_id, dim_comb_id, 
      original_cell_index, cell_index, cali_shift, 
      deleted, create_time, create_user_id, 
      update_time, update_user_id)
    values (#{id,jdbcType=CHAR}, #{orgId,jdbcType=CHAR}, #{calimeetId,jdbcType=CHAR}, 
      #{userId,jdbcType=CHAR}, #{calimeetRecordId,jdbcType=CHAR}, #{dimCombId,jdbcType=CHAR}, 
      #{originalCellIndex,jdbcType=INTEGER}, #{cellIndex,jdbcType=INTEGER}, #{caliShift,jdbcType=INTEGER}, 
      #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=CHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=CHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetRecordItemPO">
    <!--@mbg.generated-->
    update rv_calimeet_record_item
    set org_id = #{orgId,jdbcType=CHAR},
      calimeet_id = #{calimeetId,jdbcType=CHAR},
      user_id = #{userId,jdbcType=CHAR},
      calimeet_record_id = #{calimeetRecordId,jdbcType=CHAR},
      dim_comb_id = #{dimCombId,jdbcType=CHAR},
      original_cell_index = #{originalCellIndex,jdbcType=INTEGER},
      cell_index = #{cellIndex,jdbcType=INTEGER},
      cali_shift = #{caliShift,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=CHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=CHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_calimeet_record_item
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.orgId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="calimeet_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.calimeetId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.userId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="calimeet_record_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.calimeetRecordId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="dim_comb_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.dimCombId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="original_cell_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.originalCellIndex,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="cell_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.cellIndex,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="cali_shift = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.caliShift,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.deleted,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.createUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.updateUserId,jdbcType=CHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=CHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_calimeet_record_item
    (id, org_id, calimeet_id, user_id, calimeet_record_id, dim_comb_id, original_cell_index, 
      cell_index, cali_shift, deleted, create_time, create_user_id, update_time, update_user_id
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.calimeetId,jdbcType=CHAR}, 
        #{item.userId,jdbcType=CHAR}, #{item.calimeetRecordId,jdbcType=CHAR}, #{item.dimCombId,jdbcType=CHAR}, 
        #{item.originalCellIndex,jdbcType=INTEGER}, #{item.cellIndex,jdbcType=INTEGER}, 
        #{item.caliShift,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.createUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=CHAR}
        )
    </foreach>
  </insert>

  <select id="pageQuery" resultType="com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetRecordVO">
    select
    a.id,
    c.id userId,
    c.username ,
    c.fullname,
    c.status,
    c.dept_name deptName,
    c.position positionName,
    a.dim_comb_id dimCombId,
    a.cali_shift caliShift,
    a.original_cell_index originalCellIndex,
    a.cell_index cellIndex,
    a.update_user_id caliUserId,
    a.update_time caliTime,
    a.calimeet_id caliMeetId,
    c.deleted

    from rv_calimeet_record_item a
    inner join rv_calimeet_user b
    on a.org_id = b.org_id
    and a.calimeet_id = b.calimeet_id
    and a.user_id = b.user_id
    and b.deleted = 0
    inner join udp_lite_user_sp c
    on b.org_id = c.org_id
    and b.user_id = c.user_id
    inner join rv_calimeet d
    on a.calimeet_id = d.id
    and d.deleted = 0
    where a.org_id = #{orgId}
    and d.xpd_id = #{query.xpdId}

    <if test="query.caliMeetIds != null and query.caliMeetIds.size() > 0">
      and a.calimeet_id in
      <foreach collection="query.caliMeetIds" item="caliMeetId" open="(" close=")" separator=",">
        #{caliMeetId}
      </foreach>

    </if>


    <choose>
      <when test="query.userStatus != null and query.userStatus == 2">
        and c.deleted = 1
      </when>
      <when test="query.userStatus != null and query.userStatus == 0">
        and c.status = #{query.userStatus}
      </when>
      <when test="query.userStatus != null and query.userStatus == 1">
        and c.status = #{query.userStatus}
      </when>
    </choose>
    <if test="query.dimCombIds != null and query.dimCombIds.size() > 0">
      and a.dim_comb_id in
      <foreach collection="query.dimCombIds" item="dimCombId" open="(" close=")" separator=",">
        #{dimCombId}
      </foreach>
    </if>
    <if test="query.scopeDeptIds != null and query.scopeDeptIds.size() > 0">
      and c.dept_id in
      <foreach collection="query.scopeDeptIds" item="deptId" open="(" close=")" separator=",">
        #{deptId}
      </foreach>
    </if>
    <if test="query.posIds != null and query.posIds.size() > 0">
      and c.position_id in
      <foreach collection="query.posIds" item="positionId" open="(" close=")">
        #{positionId}
      </foreach>
    </if>
    <if test="query.gradeIds != null and query.gradeIds.size() > 0">
      and c.grade_id
      <foreach collection="query.gradeIds" item="gradeId" open="(" close=")">
        #{gradeId}
      </foreach>
    </if>
    <if test="query.searchKey != null and query.searchKey != ''">
      and (c.username like concat('%', #{searchKey}, '%') or c.fullname like concat('%', #{searchKey}, '%'))
    </if>
    order by a.create_time desc, a.id
  </select>


  <select id="listAll" resultType="com.yxt.talent.rv.application.calimeet.dto.CaliRecordUserDTO">
    select a.id,
           c.id                  userId,
           c.username,
           c.fullname,
           c.status,
           c.dept_name           deptName,
           c.position            positionName,
           c.grade_name          gradeName,
           c.deleted,
           a.dim_comb_id         dimCombId,
           a.cali_shift          caliShift,
           a.original_cell_index originalCellIndex,
           a.cell_index          cellIndex,
           a.update_user_id      caliUserId,
           a.update_time         caliTime,
           d.reason,
           d.suggestion,
           d.cali_details        caliDetails,
           d.result_details      resultDetails
    from rv_calimeet_record_item a
           inner join rv_calimeet_user b
                      on a.org_id = b.org_id
                        and a.calimeet_id = b.calimeet_id
                        and a.user_id = b.user_id
                        and b.deleted = 0
           inner join udp_lite_user_sp c
                      on b.org_id = c.org_id
                        and b.user_id = c.user_id
           left join rv_calimeet_record d
                     on a.org_id = d.org_id and a.calimeet_record_id = d.id
    where a.org_id = #{orgId}
      and a.calimeet_id = #{calimeetId}

    order by a.create_time desc, a.id
  </select>

  <update id="deleteByUserIds">
    update rv_calimeet_record_item
    <set>
      deleted = 1,
      update_time = now(),
      update_user_id = #{operatorId}
    </set>
    where org_id = #{orgId}
    and calimeet_id = #{caliMeetId}
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
          #{userId}
        </foreach>
      </when>
    </choose>
  </update>
</mapper>