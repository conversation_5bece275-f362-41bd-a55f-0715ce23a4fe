<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetRecordMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetRecordPO">
    <!--@mbg.generated-->
    <!--@Table rv_calimeet_record-->
    <id column="id" jdbcType="CHAR" property="id" />
    <result column="org_id" jdbcType="CHAR" property="orgId" />
    <result column="calimeet_id" jdbcType="CHAR" property="calimeetId" />
    <result column="user_id" jdbcType="CHAR" property="userId" />
    <result column="suggestion" jdbcType="LONGVARCHAR" property="suggestion" />
    <result column="reason" jdbcType="LONGVARCHAR" property="reason" />
    <result column="cali_details" jdbcType="VARCHAR" property="caliDetails" />
    <result column="result_details" jdbcType="VARCHAR" property="resultDetails" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_user_id" jdbcType="CHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="CHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, calimeet_id, user_id, suggestion, reason, cali_details, result_details, 
    deleted, create_user_id, create_time, update_user_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_calimeet_record
    where id = #{id,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_calimeet_record
    where id = #{id,jdbcType=CHAR}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetRecordPO">
    <!--@mbg.generated-->
    insert into rv_calimeet_record (id, org_id, calimeet_id, 
      user_id, suggestion, reason, 
      cali_details, result_details, deleted, 
      create_user_id, create_time, update_user_id, 
      update_time)
    values (#{id,jdbcType=CHAR}, #{orgId,jdbcType=CHAR}, #{calimeetId,jdbcType=CHAR}, 
      #{userId,jdbcType=CHAR}, #{suggestion,jdbcType=LONGVARCHAR}, #{reason,jdbcType=LONGVARCHAR}, 
      #{caliDetails,jdbcType=VARCHAR}, #{resultDetails,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, 
      #{createUserId,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=CHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetRecordPO">
    <!--@mbg.generated-->
    update rv_calimeet_record
    set org_id = #{orgId,jdbcType=CHAR},
      calimeet_id = #{calimeetId,jdbcType=CHAR},
      user_id = #{userId,jdbcType=CHAR},
      suggestion = #{suggestion,jdbcType=LONGVARCHAR},
      reason = #{reason,jdbcType=LONGVARCHAR},
      cali_details = #{caliDetails,jdbcType=VARCHAR},
      result_details = #{resultDetails,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      create_user_id = #{createUserId,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=CHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_calimeet_record
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.orgId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="calimeet_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.calimeetId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.userId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="suggestion = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.suggestion,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.reason,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="cali_details = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.caliDetails,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="result_details = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.resultDetails,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.deleted,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.createUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.updateUserId,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=CHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=CHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_calimeet_record
    (id, org_id, calimeet_id, user_id, suggestion, reason, cali_details, result_details, 
      deleted, create_user_id, create_time, update_user_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.calimeetId,jdbcType=CHAR}, 
        #{item.userId,jdbcType=CHAR}, #{item.suggestion,jdbcType=LONGVARCHAR}, #{item.reason,jdbcType=LONGVARCHAR}, 
        #{item.caliDetails,jdbcType=VARCHAR}, #{item.resultDetails,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, 
        #{item.createUserId,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateUserId,jdbcType=CHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="deleteByUserIds">
    update rv_calimeet_record
    <set>
      deleted = 1,
      update_time = now(),
      update_user_id = #{operatorId}
    </set>
    where org_id = #{orgId}
    and calimeet_id = #{caliMeetId}
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and user_id in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </update>

  <select id="getRecordDetail" resultType="com.yxt.talent.rv.application.xpd.common.dto.CalimeetDimResultDto">
    select b.suggestion,
           b.reason,
           b.result_details
    from rv_calimeet_record_item a
           inner join rv_calimeet_record b
                      on a.org_id = b.org_id and a.calimeet_record_id = b.id
                        and b.deleted = 0
    where a.org_id = #{orgId}
      and a.id = #{id}
  </select>
</mapper>